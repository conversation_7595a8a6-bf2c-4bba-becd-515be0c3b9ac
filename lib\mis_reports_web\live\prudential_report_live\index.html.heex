<style>
    *{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    }
    div.ex1 {
    height: 840px;
    
    margin-top:3%;
    
    }
    .bold{
    font-weight: 900;
    }
    .light{
    font-weight: 100;
    }
  
    .invoice_wrapper{
    width: 100%;
    max-width: 110%;
    border: 1px solid none
    }
    .invoice_wrapper .header .logo_invoice_wrap,
    .invoice_wrapper .header .bill_total_wrap{
    display:flex;
    justify-content: space-between;
    padding: 30px;
    }
    .invoice_wrapper .header .logo_sec .title_wrap{
    margin-left: 5px;
    }
    .invoice_wrapper .header .logo_sec .title_wrap .title{
    text-transform: uppercase ;
    font-size: 18px;
    color: #0C40A2;
    }
    .invoice_wrapper .header .logo_sec .title_wrap .sub_title{
    font-size: 12px;
    }
    .invoice_wrapper .header .invoice_sec,
    .invoice_wrapper .header .total_wrap{
    text-align: right;
    }
    .invoice_wrapper .header .invoice_sec .invoice{
    font-size: 28px;
    color:blue;
    }
    .invoice_wrapper .header .invoice_sec .invoice_no,
    .invoice_wrapper .header .invoice_sec .date{
    display: flex;
    width: 100%;
    }
    .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
    .invoice_wrapper .header .invoice_sec .date span:first-child{
    width: 70%;
    text-align: left;
    }
    .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
    .invoice_wrapper .header .invoice_sec .date span:first-child{
    width: calc(100%  -70px);
    }
    .invoice_wrapper .header .bill_total_wrap .name
    .invoice_wrapper .header .bill_total_wrap .price{
    font-size: 20px;
    }
    .invoice_wrapper .body .main_table .table_header{
    border-bottom: 1px solid #000;
    }
    .invoice_wrapper .body .main_table .table_header .row{
    color:#000;
    font-size: 18px;
    border-bottom: 0px;
    }
    .invoice_wrapper .body .main_table  .row{
    display: flex;
    border-bottom: 1px solid #e9e9e9;
    }
    .invoice_wrapper .body .main_table .row .col{
    padding: 9px;
    }
    .invoice_wrapper .body .main_table .row .col.col_no{width: 8%;}
    .invoice_wrapper .body .main_table .row .col.col_des{width: 30%;  text-align: center;}
    .invoice_wrapper .body .main_table .row .col.col_price{width: 23%; text-align: center;}
    .invoice_wrapper .body .main_table .row .col.col_qty{width: 21%;  text-align: center;}
    .invoice_wrapper .body .main_table .row .col.col_total{width: 20%;  text-align: center;}
    .invoice_wrapper .body .paymethod_grantotal_wrap{
    display: flex;
    justify-content: space-between;
    padding: 5px 0 30px;
    align-items: flex-end;
    padding-top: 2rem;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .paymethod_sec{
    padding-left: 30px;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec{
    width: 20%;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p{
    display: flex;
    width: 100%;
    padding-bottom: 5px;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span{
    padding: 0 10px;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span:first-child{
    width: 60%;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span:last-child span{
    width: 40%;
    text-align: right;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p:last-child span{
    border-bottom: 1px solid #000;
    }
    .invoice_wrapper .footer{
    padding:30px;
    }
    .invoice_wrapper .footer{
    padding:30px;
    }
    .kanja
    {
    font-size:16px;
    }
    .kanja_peter
    {
    font-size:13px;
    margin-top:1rem
    }
    table {
    width: 100%;
    margin-left:2%;
    }
    table th {
    border: solid 1px gray;
    text-align: left;
    }
    .kanja_chileshe
    {
    font-weight:100;
    background-color:#ffff99;
    }
    .kanja_p
    {
    font-weight:100;
    background-color:#c0c0c0;
    }
 
    .peter{
    text-align: right;
    }
    
    .p_kanja
    {
    font-weight:100;
    background-color:#ccccff;
    }
    .man{
    font-weght:400;
    }
    .bold{
    font-weight:700
    }
 
    .class-name{
     transition: all 1.5s;
     animation: animation-name 0.5s linear;
   }

  .custom-progress {
    @apply bg-blue-500;
  }
  .chileshe{
    border-radius:20px
    }

   @keyframes animation-name{
     from{
       transform: translateX(-100%);
       height: 0px;
     }
   }
     .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
  
  .status-indicator.completed {
    background-color: #10B981;
  }
  
  .status-indicator.pending {
    background-color: #F59E0B;
  }
 </style>
 
  
 <body>
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 min-h-screen py-8">
      <div class="wrapper ex1 class-name max-w-7xl mx-auto">
        <div class="invoice_wrapper rounded-lg shadow-xl bg-white" id="to_print">
          <!-- Header Section with Icon -->
          <div class="bg-gradient-to-r from-indigo-600 to-blue-500 px-8 py-6 rounded-t-lg">
            <div class="flex items-center">
              <div class="p-3 bg-white bg-opacity-20 rounded-lg">
                <%= case @process_id do %>
                  <% "1000" -> %>
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" 
                            stroke-linejoin="round" 
                            stroke-width="2" 
                            d="M3 6l9-4 9 4v2a2 2 0 01-2 2H5a2 2 0 01-2-2V6zm0 12a2 2 0 002 2h14a2 2 0 002-2v-2a2 2 0 00-2-2H5a2 2 0 00-2 2v2zm12-8v8m-8-8v8" />
                    </svg>
                  <% "2000" -> %>
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" 
                            stroke-linejoin="round" 
                            stroke-width="2" 
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  <% _ -> %>
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" 
                            stroke-linejoin="round" 
                            stroke-width="2" 
                            d="M3 6l9-4 9 4v2a2 2 0 01-2 2H5a2 2 0 01-2-2V6zm0 12a2 2 0 002 2h14a2 2 0 002-2v-2a2 2 0 00-2-2H5a2 2 0 00-2 2v2zm12-8v8m-8-8v8" />
                    </svg>
                <% end %>
              </div>
              <div class="ml-4">
                <%= case @process_id do %>
                  <% "1000" -> %>
                    <h1 class="text-2xl font-bold text-white">Prudential Report Progress</h1>
                    <p class="text-blue-100 mt-1">Reference: <%= @reference %></p>
                  <% "2000" -> %>
                    <h1 class="text-2xl font-bold text-white">Weekly Returns Report Progress</h1>
                    <p class="text-blue-100 mt-1">Reference: <%= @reference %></p>
                  <% _ -> %>
                    <h1 class="text-2xl font-bold text-white">Report Progress</h1>
                    <p class="text-blue-100 mt-1">Reference: <%= @reference %></p>
                <% end %>
              </div>
            </div>
          </div>

          <div class="body p-8">
            <!-- Progress Overview -->
            <div class="mb-8 bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div class="flex justify-between mb-4">
                <div>
                  <h3 class="text-lg font-medium text-gray-900">Overall Progress</h3>
                  <p class="text-sm text-gray-600">
                    <%= @completed_mandatory %> of <%= @total_mandatory %> mandatory tasks completed
                  </p>
                </div>
                <div class="text-right">
                  <span class="text-2xl font-bold text-blue-600"><%= @completion_percentage %>%</span>
                </div>
              </div>
              
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div class="bg-blue-600 h-2.5 rounded-full transition-all duration-500" 
                     style={"width: #{@completion_percentage}%"}
                     aria-hidden="true">
                </div>
              </div>
            </div>

            <!-- Status Cards Grid -->
            <div class="grid grid-cols-1 gap-8 md:grid-cols-2">
              <!-- Credit Files Card -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-100 transition duration-300 hover:shadow-lg">
                <div class="border-b border-gray-100 p-6">
                  <div class="flex items-center">
                    <div class="p-2 bg-blue-50 rounded-lg">
                      <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                    </div>
                    <h2 class="ml-3 text-lg font-semibold text-gray-900">Credit Files Status</h2>
                  </div>
                </div>

                <div class="p-6">
                  <%= for file_type <- @credit_files do %>
                    <div class="flex items-center py-2 group">
                      <input type="checkbox" 
                             id={"status-credit-#{file_type.value}"} 
                             checked={MisReports.Workflow.is_main_task_complete?(file_type.process_code, @reference)} 
                             disabled 
                             class="w-4 h-4 text-blue-600 rounded-full">
                      <div class="ml-3 text-sm text-gray-600 hover:text-blue-600 flex-1 group-hover:translate-x-1 transition-transform duration-200">
                        <%= file_type.label %>
                        <%= if !is_mandatory?(file_type.process_code) do %>
                          <span class="ml-2 text-xs px-2 py-1 bg-gray-100 text-gray-500 rounded-full">Optional</span>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>

              <!-- Finance Files Card -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-100 transition duration-300 hover:shadow-lg">
                <div class="border-b border-gray-100 p-6">
                  <div class="flex items-center">
                    <div class="p-2 bg-green-50 rounded-lg">
                      <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    <h2 class="ml-3 text-lg font-semibold text-gray-900">Finance Files Status</h2>
                  </div>
                </div>

                <div class="p-6">
                  <%= for file_type <- @finance_files do %>
                    <div class="flex items-center py-2 group">
                      <input type="checkbox" 
                             id={"status-finance-#{file_type.value}"} 
                             checked={MisReports.Workflow.is_main_task_complete?(file_type.process_code, @reference)} 
                             disabled 
                             class="w-4 h-4 text-green-600 rounded-full">
                      <div class="ml-3 text-sm text-gray-600 hover:text-green-600 flex-1 group-hover:translate-x-1 transition-transform duration-200">
                        <%= file_type.label %>
                        <%= if !is_mandatory?(file_type.process_code) do %>
                          <span class="ml-2 text-xs px-2 py-1 bg-gray-100 text-gray-500 rounded-full">Optional</span>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>

            <!-- Add after the existing cards in the grid -->
            <div class="grid grid-cols-1 gap-8 md:grid-cols-2 mt-8">
            <!-- Credit Maintenance Card -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-100 transition duration-300 hover:shadow-lg">
                <div class="border-b border-gray-100 p-6">
                  <div class="flex items-center">
                    <div class="p-2 bg-amber-50 rounded-lg">
                      <svg class="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                      </svg>
                    </div>
                    <h2 class="ml-3 text-lg font-semibold text-gray-900">Credit Maintenance</h2>
                  </div>
                </div>

                <div class="p-6">
                  <%= for item <- @credit_maintenance do %>
                    <div class="flex items-center py-2 group">
                      <input type="checkbox" 
                             id={"status-credit-maintenance-#{item.value}"} 
                             checked={MisReports.Workflow.is_main_task_complete?(item.process_code, @reference)} 
                             disabled 
                             class="w-4 h-4 text-amber-600 rounded-full">
                      <div class="ml-3 text-sm text-gray-600 hover:text-amber-600 flex-1 group-hover:translate-x-1 transition-transform duration-200">
                        <%= item.label %>
                        <%= if !is_mandatory?(item.process_code) do %>
                          <span class="ml-2 text-xs px-2 py-1 bg-gray-100 text-gray-500 rounded-full">Optional</span>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
              <!-- Finance Maintenance Card -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-100 transition duration-300 hover:shadow-lg">
                <div class="border-b border-gray-100 p-6">
                  <div class="flex items-center">
                    <div class="p-2 bg-purple-50 rounded-lg">
                      <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <h2 class="ml-3 text-lg font-semibold text-gray-900">Finance Maintenance</h2>
                  </div>
                </div>

                <div class="p-6">
                  <%= for item <- @finance_maintenance do %>
                    <div class="flex items-center py-2 group">
                      <input type="checkbox" 
                             id={"status-finance-maintenance-#{item.value}"} 
                             checked={MisReports.Workflow.is_main_task_complete?(item.process_code, @reference)} 
                             disabled 
                             class="w-4 h-4 text-purple-600 rounded-full">
                      <div class="ml-3 text-sm text-gray-600 hover:text-purple-600 flex-1 group-hover:translate-x-1 transition-transform duration-200">
                        <%= item.label %>
                        <%= if !is_mandatory?(item.process_code) do %>
                          <span class="ml-2 text-xs px-2 py-1 bg-gray-100 text-gray-500 rounded-full">Optional</span>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>

              
            </div>

            <!-- Maintenance Items -->
            <%!-- <div class="maintenance-item">
              <%= for item <- @maintenance_items do %>
                <% task_details = MisReports.Workflow.get_task_details(@reference, false) %>
                <% last_step = get_last_step(task_details) %>
                <% status = get_step_status(last_step) %>
                
                <div class="flex items-center py-2 group">
                  <div class={"status-indicator #{status}"}></div>
                  <div class="ml-3 flex-1">
                    <span class="text-sm text-gray-600"><%= item.label %></span>
                    <%= if last_step do %>
                      <span class="text-xs text-gray-400 ml-2">
                        (<%= last_step.step_name || "Step #{last_step.step_id}" %>)
                      </span>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div> --%>
          </div>
        </div>
      </div>
    </div>
 </body>

<!-- Comments Section (matching Weekly Live functionality) -->
<%= if @step_id && normalize_step_name(MisReports.Workflow.get_wklstep_rule!(@step_id).step_name) in @review_steps and @reference and not @view_only do %>
  <div class="mt-8 bg-white rounded-lg shadow p-6">
    <!-- Comments History -->
    <div class="comments-history mb-4">
      <div class="bg-white rounded-lg shadow p-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
          <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
          </svg>
          Review History
        </h3>

        <%= if @comments && length(@comments) > 0 do %>
          <div class="space-y-4">
            <%= for %{action_id: action_id, comments: comments, created_at: created_at, step_id: step_id, user_name: user_name} <- @comments do %>
              <div class="flex gap-4 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                    <span class="text-sm font-medium text-indigo-600">
                      <%= String.slice(user_name || "", 0..1) %>
                    </span>
                  </div>
                </div>
                <div class="flex-1">
                  <div class="flex items-center justify-between mb-1">
                    <span class="text-sm font-medium text-gray-900">
                      <%= user_name %>
                    </span>
                    <span class="text-xs text-gray-500">
                      <%= NaiveDateTime.to_string(created_at) %>
                    </span>
                  </div>
                  <p class="text-sm text-gray-700 whitespace-pre-wrap"><%= comments %></p>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <p class="text-sm text-gray-500 italic">No previous comments</p>
        <% end %>
      </div>
    </div>

    <!-- Schedule Comment Buttons -->
    <div class="mb-6">
      <h4 class="text-md font-medium text-gray-900 mb-3">Add Comments to Schedules</h4>
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        <%= for schedule <- @prudential_schedules do %>
          <button
            type="button"
            class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            phx-click="open_comment_modal"
            phx-value-schedule={schedule}
          >
            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
            </svg>
            <%= format_schedule_name(schedule) %>
          </button>
        <% end %>
      </div>
    </div>

    <!-- Review Form -->
    <form phx-submit="save" class="comment-form">
      <div class="mb-4">
        <label for="comment" class="block text-sm font-medium text-gray-700 mb-2">
          Review Comments
          <%= if @comment && String.trim(@comment) != "" do %>
            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              <%= count_comments(@comment) %> schedule comment(s) included
            </span>
          <% end %>
        </label>
        <div class="relative">
          <!-- Individual Comments Display with Delete Buttons -->
          <%
            comment_list = get_comment_list_for_display(@pending_comments || %{})
          %>
          <%= if length(comment_list) > 0 do %>
            <div class="space-y-2 mb-4 p-3 bg-gray-50 rounded-md border">
              <div class="flex items-center justify-between mb-2">
                <h4 class="text-sm font-medium text-gray-700">Schedule Comments</h4>
                <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800">
                  <%= length(comment_list) %> comment(s)
                </span>
              </div>

              <%= for comment <- comment_list do %>
                <div class="flex items-start justify-between p-2 bg-white rounded border hover:bg-gray-50 transition-colors">
                  <div class="flex-1 min-w-0">
                    <div class="text-sm text-gray-900">
                      <span class="font-medium text-blue-600"><%= comment.schedule_name %></span>
                      <span class="text-gray-500 mx-2">-</span>
                      <span><%= comment.comment_text %></span>
                    </div>
                  </div>
                  <button
                    type="button"
                    class="ml-2 flex-shrink-0 inline-flex items-center justify-center w-6 h-6 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-full transition-colors"
                    phx-click="delete_comment"
                    phx-value-schedule={comment.schedule_id}
                    data-confirm={"Are you sure you want to delete this comment for #{comment.schedule_name}?"}
                    title={"Delete comment for #{comment.schedule_name}"}
                  >
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              <% end %>
            </div>
          <% end %>

          <!-- Hidden textarea for form submission (maintains compatibility) -->
          <textarea
            id="comment"
            name="comment"
            class="comment-textarea hidden"
            phx-hook="CommentTextarea"
            data-comment={@comment || ""}
          ><%= @comment || "" %></textarea>

          <!-- Additional review comments textarea -->
          <div class="mt-2">
            <label for="additional-comments" class="block text-sm font-medium text-gray-700 mb-1">
              Additional Review Comments (Optional)
            </label>
            <textarea
              id="additional-comments"
              name="additional_comments"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Add any additional review comments here..."
              rows="4"
              phx-hook="AdditionalComments"
              phx-debounce="300"
              data-reference={@reference}
              value={@additional_comments}
            ><%= @additional_comments %></textarea>
          </div>
        </div>
        <%= if @comment && String.trim(@comment) != "" do %>
          <div class="mt-2 text-xs text-gray-600">
            <strong>Note:</strong> This field contains automatically aggregated comments from individual schedules.
            You can add additional review comments above the existing content.
          </div>
        <% end %>
      </div>
      <div class="comment-actions flex gap-3">
        <button
          type="submit"
          name="action"
          value="97"
          class="rounded-md bg-green-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          Approve
        </button>
        <button
          type="submit"
          name="action"
          value="96"
          class="rounded-md bg-red-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          Reject
        </button>
      </div>
    </form>
  </div>
<% end %>

<!-- Comment Modal (matching Weekly Live functionality) -->
<%= if @show_comment_modal do %>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
              <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
              </svg>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <%
                # Check if this schedule has an existing comment
                has_existing_comment = @pending_comments && @current_schedule && Map.has_key?(@pending_comments, @current_schedule)
                modal_title = if has_existing_comment, do: "Edit Comment", else: "Add Comment"
              %>
              <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                 <%= modal_title %> on <%= @current_schedule_name || "Schedule" %>
                 <%= if has_existing_comment do %>
                   <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                     Editing
                   </span>
                 <% end %>
              </h3>
              <div class="mt-4">
                <form id="comment-form" phx-submit="save_comment">
                  <div>
                    <label for="comment" class="block text-sm font-medium text-gray-700">
                      Comment for <%= @current_schedule_name || "Schedule" %>
                    </label>
                    <div class="mt-1">
                      <textarea
                        id="modal-comment"
                        name="comment"
                        rows="4"
                        class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        placeholder="Enter your comment here..."
                        value={@modal_comment}
                        required
                      ></textarea>
                    </div>
                    <%= if @comment && String.trim(@comment) != "" do %>
                      <div class="mt-2 p-2 bg-gray-50 rounded-md">
                        <p class="text-xs text-gray-600 mb-1">
                          Existing comments (<%= count_comments(@comment) %> total):
                        </p>
                        <div class="text-xs text-gray-700 max-h-20 overflow-y-auto">
                          <%= for line <- String.split(@comment, "\n") do %>
                            <%= if String.trim(line) != "" do %>
                              <div class="mb-1"><%= line %></div>
                            <% end %>
                          <% end %>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            type="submit"
            form="comment-form"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Save Comment
          </button>
          <button
            type="button"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            phx-click="close_comment_modal"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
<% end %>

