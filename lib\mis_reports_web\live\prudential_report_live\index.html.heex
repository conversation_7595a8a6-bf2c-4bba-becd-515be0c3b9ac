<style>
    *{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    }
    div.ex1 {
    height: 840px;
    
    margin-top:3%;
    
    }
    .bold{
    font-weight: 900;
    }
    .light{
    font-weight: 100;
    }
  
    .invoice_wrapper{
    width: 100%;
    max-width: 110%;
    border: 1px solid none
    }
    .invoice_wrapper .header .logo_invoice_wrap,
    .invoice_wrapper .header .bill_total_wrap{
    display:flex;
    justify-content: space-between;
    padding: 30px;
    }
    .invoice_wrapper .header .logo_sec .title_wrap{
    margin-left: 5px;
    }
    .invoice_wrapper .header .logo_sec .title_wrap .title{
    text-transform: uppercase ;
    font-size: 18px;
    color: #0C40A2;
    }
    .invoice_wrapper .header .logo_sec .title_wrap .sub_title{
    font-size: 12px;
    }
    .invoice_wrapper .header .invoice_sec,
    .invoice_wrapper .header .total_wrap{
    text-align: right;
    }
    .invoice_wrapper .header .invoice_sec .invoice{
    font-size: 28px;
    color:blue;
    }
    .invoice_wrapper .header .invoice_sec .invoice_no,
    .invoice_wrapper .header .invoice_sec .date{
    display: flex;
    width: 100%;
    }
    .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
    .invoice_wrapper .header .invoice_sec .date span:first-child{
    width: 70%;
    text-align: left;
    }
    .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
    .invoice_wrapper .header .invoice_sec .date span:first-child{
    width: calc(100%  -70px);
    }
    .invoice_wrapper .header .bill_total_wrap .name
    .invoice_wrapper .header .bill_total_wrap .price{
    font-size: 20px;
    }
    .invoice_wrapper .body .main_table .table_header{
    border-bottom: 1px solid #000;
    }
    .invoice_wrapper .body .main_table .table_header .row{
    color:#000;
    font-size: 18px;
    border-bottom: 0px;
    }
    .invoice_wrapper .body .main_table  .row{
    display: flex;
    border-bottom: 1px solid #e9e9e9;
    }
    .invoice_wrapper .body .main_table .row .col{
    padding: 9px;
    }
    .invoice_wrapper .body .main_table .row .col.col_no{width: 8%;}
    .invoice_wrapper .body .main_table .row .col.col_des{width: 30%;  text-align: center;}
    .invoice_wrapper .body .main_table .row .col.col_price{width: 23%; text-align: center;}
    .invoice_wrapper .body .main_table .row .col.col_qty{width: 21%;  text-align: center;}
    .invoice_wrapper .body .main_table .row .col.col_total{width: 20%;  text-align: center;}
    .invoice_wrapper .body .paymethod_grantotal_wrap{
    display: flex;
    justify-content: space-between;
    padding: 5px 0 30px;
    align-items: flex-end;
    padding-top: 2rem;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .paymethod_sec{
    padding-left: 30px;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec{
    width: 20%;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p{
    display: flex;
    width: 100%;
    padding-bottom: 5px;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span{
    padding: 0 10px;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span:first-child{
    width: 60%;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span:last-child span{
    width: 40%;
    text-align: right;
    }
    .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p:last-child span{
    border-bottom: 1px solid #000;
    }
    .invoice_wrapper .footer{
    padding:30px;
    }
    .invoice_wrapper .footer{
    padding:30px;
    }
    .kanja
    {
    font-size:16px;
    }
    .kanja_peter
    {
    font-size:13px;
    margin-top:1rem
    }
    table {
    width: 100%;
    margin-left:2%;
    }
    table th {
    border: solid 1px gray;
    text-align: left;
    }
    .kanja_chileshe
    {
    font-weight:100;
    background-color:#ffff99;
    }
    .kanja_p
    {
    font-weight:100;
    background-color:#c0c0c0;
    }
 
    .peter{
    text-align: right;
    }
    
    .p_kanja
    {
    font-weight:100;
    background-color:#ccccff;
    }
    .man{
    font-weght:400;
    }
    .bold{
    font-weight:700
    }
 
    .class-name{
     transition: all 1.5s;
     animation: animation-name 0.5s linear;
   }

  .custom-progress {
    @apply bg-blue-500;
  }
  .chileshe{
    border-radius:20px
    }

   @keyframes animation-name{
     from{
       transform: translateX(-100%);
       height: 0px;
     }
   }
     .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
  
  .status-indicator.completed {
    background-color: #10B981;
  }
  
  .status-indicator.pending {
    background-color: #F59E0B;
  }
 </style>
 
  
 <body>
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 min-h-screen py-8">
      <div class="wrapper ex1 class-name max-w-7xl mx-auto">
        <div class="invoice_wrapper rounded-lg shadow-xl bg-white" id="to_print">
          <!-- Header Section with Icon -->
          <div class="bg-gradient-to-r from-indigo-600 to-blue-500 px-8 py-6 rounded-t-lg">
            <div class="flex items-center">
              <div class="p-3 bg-white bg-opacity-20 rounded-lg">
                <%= case @process_id do %>
                  <% "1000" -> %>
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" 
                            stroke-linejoin="round" 
                            stroke-width="2" 
                            d="M3 6l9-4 9 4v2a2 2 0 01-2 2H5a2 2 0 01-2-2V6zm0 12a2 2 0 002 2h14a2 2 0 002-2v-2a2 2 0 00-2-2H5a2 2 0 00-2 2v2zm12-8v8m-8-8v8" />
                    </svg>
                  <% "2000" -> %>
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" 
                            stroke-linejoin="round" 
                            stroke-width="2" 
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  <% _ -> %>
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" 
                            stroke-linejoin="round" 
                            stroke-width="2" 
                            d="M3 6l9-4 9 4v2a2 2 0 01-2 2H5a2 2 0 01-2-2V6zm0 12a2 2 0 002 2h14a2 2 0 002-2v-2a2 2 0 00-2-2H5a2 2 0 00-2 2v2zm12-8v8m-8-8v8" />
                    </svg>
                <% end %>
              </div>
              <div class="ml-4">
                <%= case @process_id do %>
                  <% "1000" -> %>
                    <h1 class="text-2xl font-bold text-white">Prudential Report Progress</h1>
                    <p class="text-blue-100 mt-1">Reference: <%= @reference %></p>
                  <% "2000" -> %>
                    <h1 class="text-2xl font-bold text-white">Weekly Returns Report Progress</h1>
                    <p class="text-blue-100 mt-1">Reference: <%= @reference %></p>
                  <% _ -> %>
                    <h1 class="text-2xl font-bold text-white">Report Progress</h1>
                    <p class="text-blue-100 mt-1">Reference: <%= @reference %></p>
                <% end %>
              </div>
            </div>
          </div>

          <div class="body p-8">
            <!-- Progress Overview -->
            <div class="mb-8 bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div class="flex justify-between mb-4">
                <div>
                  <h3 class="text-lg font-medium text-gray-900">Overall Progress</h3>
                  <p class="text-sm text-gray-600">
                    <%= @completed_mandatory %> of <%= @total_mandatory %> mandatory tasks completed
                  </p>
                </div>
                <div class="text-right">
                  <span class="text-2xl font-bold text-blue-600"><%= @completion_percentage %>%</span>
                </div>
              </div>
              
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div class="bg-blue-600 h-2.5 rounded-full transition-all duration-500" 
                     style={"width: #{@completion_percentage}%"}
                     aria-hidden="true">
                </div>
              </div>
            </div>

            <!-- Status Cards Grid -->
            <div class="grid grid-cols-1 gap-8 md:grid-cols-2">
              <!-- Credit Files Card -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-100 transition duration-300 hover:shadow-lg">
                <div class="border-b border-gray-100 p-6">
                  <div class="flex items-center">
                    <div class="p-2 bg-blue-50 rounded-lg">
                      <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                    </div>
                    <h2 class="ml-3 text-lg font-semibold text-gray-900">Credit Files Status</h2>
                  </div>
                </div>

                <div class="p-6">
                  <%= for file_type <- @credit_files do %>
                    <div class="flex items-center py-2 group">
                      <input type="checkbox" 
                             id={"status-credit-#{file_type.value}"} 
                             checked={MisReports.Workflow.is_main_task_complete?(file_type.process_code, @reference)} 
                             disabled 
                             class="w-4 h-4 text-blue-600 rounded-full">
                      <div class="ml-3 text-sm text-gray-600 hover:text-blue-600 flex-1 group-hover:translate-x-1 transition-transform duration-200">
                        <%= file_type.label %>
                        <%= if !is_mandatory?(file_type.process_code) do %>
                          <span class="ml-2 text-xs px-2 py-1 bg-gray-100 text-gray-500 rounded-full">Optional</span>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>

              <!-- Finance Files Card -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-100 transition duration-300 hover:shadow-lg">
                <div class="border-b border-gray-100 p-6">
                  <div class="flex items-center">
                    <div class="p-2 bg-green-50 rounded-lg">
                      <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    <h2 class="ml-3 text-lg font-semibold text-gray-900">Finance Files Status</h2>
                  </div>
                </div>

                <div class="p-6">
                  <%= for file_type <- @finance_files do %>
                    <div class="flex items-center py-2 group">
                      <input type="checkbox" 
                             id={"status-finance-#{file_type.value}"} 
                             checked={MisReports.Workflow.is_main_task_complete?(file_type.process_code, @reference)} 
                             disabled 
                             class="w-4 h-4 text-green-600 rounded-full">
                      <div class="ml-3 text-sm text-gray-600 hover:text-green-600 flex-1 group-hover:translate-x-1 transition-transform duration-200">
                        <%= file_type.label %>
                        <%= if !is_mandatory?(file_type.process_code) do %>
                          <span class="ml-2 text-xs px-2 py-1 bg-gray-100 text-gray-500 rounded-full">Optional</span>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>

            <!-- Add after the existing cards in the grid -->
            <div class="grid grid-cols-1 gap-8 md:grid-cols-2 mt-8">
            <!-- Credit Maintenance Card -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-100 transition duration-300 hover:shadow-lg">
                <div class="border-b border-gray-100 p-6">
                  <div class="flex items-center">
                    <div class="p-2 bg-amber-50 rounded-lg">
                      <svg class="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                      </svg>
                    </div>
                    <h2 class="ml-3 text-lg font-semibold text-gray-900">Credit Maintenance</h2>
                  </div>
                </div>

                <div class="p-6">
                  <%= for item <- @credit_maintenance do %>
                    <div class="flex items-center py-2 group">
                      <input type="checkbox" 
                             id={"status-credit-maintenance-#{item.value}"} 
                             checked={MisReports.Workflow.is_main_task_complete?(item.process_code, @reference)} 
                             disabled 
                             class="w-4 h-4 text-amber-600 rounded-full">
                      <div class="ml-3 text-sm text-gray-600 hover:text-amber-600 flex-1 group-hover:translate-x-1 transition-transform duration-200">
                        <%= item.label %>
                        <%= if !is_mandatory?(item.process_code) do %>
                          <span class="ml-2 text-xs px-2 py-1 bg-gray-100 text-gray-500 rounded-full">Optional</span>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
              <!-- Finance Maintenance Card -->
              <div class="bg-white rounded-xl shadow-sm border border-gray-100 transition duration-300 hover:shadow-lg">
                <div class="border-b border-gray-100 p-6">
                  <div class="flex items-center">
                    <div class="p-2 bg-purple-50 rounded-lg">
                      <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <h2 class="ml-3 text-lg font-semibold text-gray-900">Finance Maintenance</h2>
                  </div>
                </div>

                <div class="p-6">
                  <%= for item <- @finance_maintenance do %>
                    <div class="flex items-center py-2 group">
                      <input type="checkbox" 
                             id={"status-finance-maintenance-#{item.value}"} 
                             checked={MisReports.Workflow.is_main_task_complete?(item.process_code, @reference)} 
                             disabled 
                             class="w-4 h-4 text-purple-600 rounded-full">
                      <div class="ml-3 text-sm text-gray-600 hover:text-purple-600 flex-1 group-hover:translate-x-1 transition-transform duration-200">
                        <%= item.label %>
                        <%= if !is_mandatory?(item.process_code) do %>
                          <span class="ml-2 text-xs px-2 py-1 bg-gray-100 text-gray-500 rounded-full">Optional</span>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>

              
            </div>

            <!-- Maintenance Items -->
            <%!-- <div class="maintenance-item">
              <%= for item <- @maintenance_items do %>
                <% task_details = MisReports.Workflow.get_task_details(@reference, false) %>
                <% last_step = get_last_step(task_details) %>
                <% status = get_step_status(last_step) %>
                
                <div class="flex items-center py-2 group">
                  <div class={"status-indicator #{status}"}></div>
                  <div class="ml-3 flex-1">
                    <span class="text-sm text-gray-600"><%= item.label %></span>
                    <%= if last_step do %>
                      <span class="text-xs text-gray-400 ml-2">
                        (<%= last_step.step_name || "Step #{last_step.step_id}" %>)
                      </span>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div> --%>
          </div>
        </div>
      </div>
    </div>
 </body>





